#!/usr/bin/env python3
"""
Script semplificato per migrare la tabella cantieri
"""

import psycopg2
import sys

def migrate_database():
    """Esegue la migrazione della tabella cantieri"""
    try:
        print("🔌 Connessione al database...")
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            database='cantieri',
            user='postgres',
            password='Taranto'
        )
        conn.autocommit = False
        cursor = conn.cursor()
        
        print("🔍 Verifica schema attuale...")
        
        # Verifica se la colonna 'nome' esiste
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'cantieri' AND column_name = 'nome'
        """)
        nome_exists = cursor.fetchone() is not None
        
        # Verifica se la colonna 'commessa' esiste
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'cantieri' AND column_name = 'commessa'
        """)
        commessa_exists = cursor.fetchone() is not None
        
        print(f"Colonna 'nome' esiste: {nome_exists}")
        print(f"Colonna 'commessa' esiste: {commessa_exists}")
        
        # Se 'nome' esiste e 'commessa' non esiste, rinomina
        if nome_exists and not commessa_exists:
            print("🔄 Rinominando colonna 'nome' → 'commessa'...")
            cursor.execute("ALTER TABLE cantieri RENAME COLUMN nome TO commessa")
            print("✅ Colonna rinominata con successo!")
        elif commessa_exists:
            print("✅ Colonna 'commessa' già esistente")
        else:
            print("❌ Situazione inaspettata: né 'nome' né 'commessa' esistono")
            return False
        
        # Aggiungi le nuove colonne se non esistono
        new_columns = [
            ("nome_cliente", "TEXT"),
            ("indirizzo_cantiere", "TEXT"),
            ("citta_cantiere", "TEXT"),
            ("nazione_cantiere", "TEXT"),
            ("riferimenti_normativi", "TEXT"),
            ("documentazione_progetto", "TEXT")
        ]
        
        for column_name, column_type in new_columns:
            cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'cantieri' AND column_name = %s
            """, (column_name,))
            
            if cursor.fetchone() is None:
                print(f"➕ Aggiungendo colonna '{column_name}'...")
                cursor.execute(f"ALTER TABLE cantieri ADD COLUMN {column_name} {column_type}")
            else:
                print(f"✅ Colonna '{column_name}' già esistente")
        
        # Imposta valori di default per i cantieri esistenti
        print("🔄 Impostando valori di default...")
        cursor.execute("""
            UPDATE cantieri
            SET
                nome_cliente = COALESCE(nome_cliente, 'Cliente da definire'),
                indirizzo_cantiere = COALESCE(indirizzo_cantiere, 'Indirizzo da definire'),
                citta_cantiere = COALESCE(citta_cantiere, 'Città da definire'),
                nazione_cantiere = COALESCE(nazione_cantiere, 'Italia'),
                riferimenti_normativi = COALESCE(riferimenti_normativi, 'CEI 64-8 Parte 6; IEC 60364-6'),
                documentazione_progetto = COALESCE(documentazione_progetto, 'Schemi elettrici e layout di progetto')
            WHERE
                nome_cliente IS NULL
                OR indirizzo_cantiere IS NULL
                OR citta_cantiere IS NULL
                OR nazione_cantiere IS NULL
                OR riferimenti_normativi IS NULL
                OR documentazione_progetto IS NULL
        """)
        
        rows_updated = cursor.rowcount
        print(f"✅ Aggiornati {rows_updated} cantieri con valori di default")
        
        # Commit delle modifiche
        conn.commit()
        print("✅ Migrazione completata con successo!")
        
        # Verifica finale
        cursor.execute("SELECT COUNT(*) FROM cantieri")
        total_cantieri = cursor.fetchone()[0]
        print(f"📊 Totale cantieri: {total_cantieri}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore durante la migrazione: {e}")
        if 'conn' in locals():
            conn.rollback()
            print("🔄 Rollback eseguito")
        return False
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
        print("🔌 Connessione database chiusa")

if __name__ == "__main__":
    success = migrate_database()
    sys.exit(0 if success else 1)
