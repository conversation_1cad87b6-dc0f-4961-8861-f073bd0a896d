{"ast": null, "code": "// Configurazione centralizzata per l'applicazione\nconst config = {\n  // URL dell'API del backend\n  API_URL: 'http://localhost:8003/api',\n  // Altre configurazioni globali possono essere aggiunte qui\n  DEFAULT_TIMEOUT: 60000 // 60 secondi (aumentato per risolvere problemi di timeout)\n};\nexport default config;", "map": {"version": 3, "names": ["config", "API_URL", "DEFAULT_TIMEOUT"], "sources": ["C:/CMS/webapp/frontend/src/config.js"], "sourcesContent": ["// Configurazione centralizzata per l'applicazione\r\nconst config = {\r\n  // URL dell'API del backend\r\n  API_URL: 'http://localhost:8003/api',\r\n\r\n  // Altre configurazioni globali possono essere aggiunte qui\r\n  DEFAULT_TIMEOUT: 60000, // 60 secondi (aumentato per risolvere problemi di timeout)\r\n};\r\n\r\nexport default config;\r\n"], "mappings": "AAAA;AACA,MAAMA,MAAM,GAAG;EACb;EACAC,OAAO,EAAE,2BAA2B;EAEpC;EACAC,eAAe,EAAE,KAAK,CAAE;AAC1B,CAAC;AAED,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}