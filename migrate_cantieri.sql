-- Script SQL per migrare la tabella cantieri
-- Rinomina 'nome' → 'commessa' e aggiunge nuove colonne

-- 1. Verifica se la colonna 'nome' esiste e 'commessa' non esiste
DO $$
BEGIN
    -- Rinomina colonna 'nome' → 'commessa' se necessario
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'cantieri' AND column_name = 'nome'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'cantieri' AND column_name = 'commessa'
    ) THEN
        ALTER TABLE cantieri RENAME COLUMN nome TO commessa;
        RAISE NOTICE 'Colonna rinominata: nome → commessa';
    ELSE
        RAISE NOTICE 'Colonna commessa già esistente o nome non trovata';
    END IF;
END $$;

-- 2. Aggiungi nuove colonne se non esistono
DO $$
BEGIN
    -- nome_cliente
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'cantieri' AND column_name = 'nome_cliente'
    ) THEN
        ALTER TABLE cantieri ADD COLUMN nome_cliente TEXT;
        RAISE NOTICE 'Aggiunta colonna: nome_cliente';
    END IF;
    
    -- indirizzo_cantiere
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'cantieri' AND column_name = 'indirizzo_cantiere'
    ) THEN
        ALTER TABLE cantieri ADD COLUMN indirizzo_cantiere TEXT;
        RAISE NOTICE 'Aggiunta colonna: indirizzo_cantiere';
    END IF;
    
    -- citta_cantiere
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'cantieri' AND column_name = 'citta_cantiere'
    ) THEN
        ALTER TABLE cantieri ADD COLUMN citta_cantiere TEXT;
        RAISE NOTICE 'Aggiunta colonna: citta_cantiere';
    END IF;
    
    -- nazione_cantiere
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'cantieri' AND column_name = 'nazione_cantiere'
    ) THEN
        ALTER TABLE cantieri ADD COLUMN nazione_cantiere TEXT;
        RAISE NOTICE 'Aggiunta colonna: nazione_cantiere';
    END IF;
    
    -- riferimenti_normativi
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'cantieri' AND column_name = 'riferimenti_normativi'
    ) THEN
        ALTER TABLE cantieri ADD COLUMN riferimenti_normativi TEXT;
        RAISE NOTICE 'Aggiunta colonna: riferimenti_normativi';
    END IF;
    
    -- documentazione_progetto
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'cantieri' AND column_name = 'documentazione_progetto'
    ) THEN
        ALTER TABLE cantieri ADD COLUMN documentazione_progetto TEXT;
        RAISE NOTICE 'Aggiunta colonna: documentazione_progetto';
    END IF;
END $$;

-- 3. Imposta valori di default per i cantieri esistenti
UPDATE cantieri
SET
    nome_cliente = COALESCE(nome_cliente, 'Cliente da definire'),
    indirizzo_cantiere = COALESCE(indirizzo_cantiere, 'Indirizzo da definire'),
    citta_cantiere = COALESCE(citta_cantiere, 'Città da definire'),
    nazione_cantiere = COALESCE(nazione_cantiere, 'Italia'),
    riferimenti_normativi = COALESCE(riferimenti_normativi, 'CEI 64-8 Parte 6; IEC 60364-6'),
    documentazione_progetto = COALESCE(documentazione_progetto, 'Schemi elettrici e layout di progetto')
WHERE
    nome_cliente IS NULL
    OR indirizzo_cantiere IS NULL
    OR citta_cantiere IS NULL
    OR nazione_cantiere IS NULL
    OR riferimenti_normativi IS NULL
    OR documentazione_progetto IS NULL;

-- 4. Mostra il risultato finale
SELECT 'Migrazione completata!' as status;
SELECT COUNT(*) as total_cantieri FROM cantieri;
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'cantieri' 
ORDER BY column_name;
