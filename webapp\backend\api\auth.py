from datetime import <PERSON><PERSON><PERSON>
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..config import settings
from ..core.security import (
    verify_password,
    create_access_token,
    get_current_active_user
)
from ..database import get_db
from ..models.user import User
from ..models.cantiere import Cantiere
from ..schemas.auth import Token, UserLogin, CantiereLogin

router = APIRouter()

@router.post("/login", response_model=Token)
def login_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
) -> Any:
    """
    Endpoint per il login standard (admin o utente standard).
    Utilizza OAuth2 con username e password.

    Args:
        form_data: Dati del form di login
        db: Sessione del database

    Returns:
        Token: Token di accesso

    Raises:
        HTTPException: Se le credenziali non sono valide
    """
    # Cerca l'utente nel database
    user = db.query(User).filter(User.username == form_data.username).first()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Username o password non corretti",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Verifica la password
    print(f"DEBUG - Verifica password per utente: {form_data.username}")
    print(f"DEBUG - Password fornita: '{form_data.password}'")
    print(f"DEBUG - Password hash nel DB: '{user.password}'")

    if not verify_password(form_data.password, user.password):
        print(f"DEBUG - Autenticazione fallita: password non corretta")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Username o password non corretti",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Verifica che l'utente sia abilitato
    if not user.abilitato:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Utente disabilitato",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Verifica se l'utente è scaduto
    import datetime
    if user.ruolo == "user" and user.data_scadenza and user.data_scadenza < datetime.date.today():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Utente scaduto",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Crea il token di accesso
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    return {
        "access_token": create_access_token(
            data={
                "sub": user.username,
                "user_id": user.id_utente,
                "role": user.ruolo
            },
            expires_delta=access_token_expires
        ),
        "token_type": "bearer",
        "user_id": user.id_utente,
        "username": user.username,
        "role": user.ruolo
    }

@router.post("/login/cantiere", response_model=Token)
def login_cantiere(
    cantiere_login: CantiereLogin,
    db: Session = Depends(get_db)
) -> Any:
    """
    Endpoint per il login utente cantiere.
    Utilizza il codice univoco del cantiere come nome utente e la password del cantiere come password.
    Verifica solo le credenziali definite nel database senza casi speciali.

    Args:
        cantiere_login: Dati di login del cantiere (codice_univoco e password)
        db: Sessione del database

    Returns:
        Token: Token di accesso

    Raises:
        HTTPException: Se le credenziali non sono valide
    """
    # Stampa informazioni di debug
    print(f"DEBUG - Tentativo di login cantiere con codice univoco: '{cantiere_login.codice_univoco}'")
    print(f"DEBUG - Password fornita: '{cantiere_login.password}'")

    # Cerca il cantiere nel database
    cantiere = db.query(Cantiere).filter(Cantiere.codice_univoco == cantiere_login.codice_univoco).first()

    if not cantiere:
        print(f"DEBUG - Cantiere con codice univoco '{cantiere_login.codice_univoco}' non trovato nel database")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Codice univoco o password non corretti",
            headers={"WWW-Authenticate": "Bearer"},
        )

    print(f"DEBUG - Cantiere trovato: ID={cantiere.id_cantiere}, Nome='{cantiere.nome}'")
    print(f"DEBUG - Password hash nel DB: '{cantiere.password_cantiere}'")

    # Verifica la password del cantiere
    print(f"DEBUG - Codice univoco: '{cantiere_login.codice_univoco}'")
    print(f"DEBUG - Password fornita: '{cantiere_login.password}'")
    print(f"DEBUG - Password nel DB (troncata): '{str(cantiere.password_cantiere)[:30]}...'")
    print(f"DEBUG - Tipo password nel DB: {type(cantiere.password_cantiere)}")

    # Verifica la password usando la funzione di verifica
    from ..core.security import verify_cantiere_password
    auth_success = verify_cantiere_password(cantiere_login.password, cantiere.password_cantiere)

    if not auth_success:
        print(f"DEBUG - Autenticazione fallita: password non corretta")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Codice univoco o password non corretti",
            headers={"WWW-Authenticate": "Bearer"},
        )

    print("DEBUG - Autenticazione riuscita!")

    # Crea il token di accesso
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    # Ottieni l'utente proprietario del cantiere
    user = db.query(User).filter(User.id_utente == cantiere.id_utente).first()

    # Stampa informazioni dettagliate sul cantiere
    print(f"DEBUG - Dettagli cantiere:")
    print(f"ID: {cantiere.id_cantiere}")
    print(f"Commessa: {cantiere.commessa}")
    print(f"Codice univoco: {cantiere.codice_univoco}")
    print(f"ID utente proprietario: {cantiere.id_utente}")

    # Crea il token con i dati del cantiere
    token_data = {
        "sub": f"cantiere_{cantiere.id_cantiere}",
        "user_id": cantiere.id_utente,
        "role": "cantieri_user",
        "cantiere_id": cantiere.id_cantiere
    }

    print(f"DEBUG - Dati token: {token_data}")

    # Crea la risposta con i dati del cantiere
    response_data = {
        "access_token": create_access_token(
            data=token_data,
            expires_delta=access_token_expires
        ),
        "token_type": "bearer",
        "user_id": cantiere.id_utente,
        "username": f"Cantiere: {cantiere.commessa}",
        "role": "cantieri_user",
        "cantiere_id": cantiere.id_cantiere,
        "cantiere_name": cantiere.commessa
    }

    print(f"DEBUG - Risposta login cantiere: {response_data}")

    return response_data

@router.post("/test-token", response_model=dict)
def test_token(current_user: User = Depends(get_current_active_user)) -> Any:
    """
    Endpoint per testare la validità del token.

    Args:
        current_user: Utente corrente

    Returns:
        dict: Dati dell'utente
    """
    # Ottieni i dati del token dall'oggetto utente
    token_data = getattr(current_user, "token_data", None)
    is_impersonated = token_data and getattr(token_data, "is_impersonated", False)
    impersonated_id = token_data and getattr(token_data, "impersonated_id", None)
    impersonated_username = token_data and getattr(token_data, "impersonated_username", None)
    impersonated_role = token_data and getattr(token_data, "impersonated_role", None)

    print(f"Test token riuscito per utente: {current_user.username}, ruolo: {current_user.ruolo}, impersonato: {is_impersonated}")
    if is_impersonated:
        print(f"Dati utente impersonato - id: {impersonated_id}, username: {impersonated_username}, ruolo: {impersonated_role}")

    response_data = {
        "user_id": current_user.id_utente,
        "username": current_user.username,
        "role": current_user.ruolo,
        "message": "Token valido",
        "is_impersonated": is_impersonated,
        "impersonated_id": impersonated_id,
        "impersonated_username": impersonated_username,
        "impersonated_role": impersonated_role
    }

    # Non c'è bisogno di aggiungere i dati dell'amministratore perché ora l'utente è già l'amministratore

    return response_data


class ImpersonateRequest(BaseModel):
    user_id: int


@router.post("/impersonate", response_model=Token)
def impersonate_user(
    impersonate_data: ImpersonateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Endpoint per impersonare un altro utente.
    Solo gli amministratori possono impersonare altri utenti.

    Args:
        impersonate_data: Dati dell'utente da impersonare
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        Token: Token di accesso per l'utente impersonato
    """
    # Verifica che l'utente sia un amministratore
    if current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questa risorsa"
        )

    # Ottieni l'utente da impersonare
    user = db.query(User).filter(User.id_utente == impersonate_data.user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Utente non trovato"
        )

    # Verifica che l'utente sia abilitato
    if not user.abilitato:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="L'utente non è abilitato"
        )

    # Crea un token per l'utente impersonato, ma include anche i dati dell'amministratore
    # per consentire il ritorno al menu amministratore
    # IMPORTANTE: L'amministratore mantiene il suo ruolo 'owner' anche durante l'impersonificazione
    # ma aggiunge informazioni sull'utente impersonato per accedere alle sue funzionalità
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={
            "sub": current_user.username,  # Mantiene l'username dell'amministratore
            "user_id": current_user.id_utente,  # Mantiene l'ID dell'amministratore
            "role": "owner",  # Mantiene il ruolo di amministratore
            "is_impersonated": True,
            "impersonated_id": user.id_utente,  # ID dell'utente impersonato
            "impersonated_username": user.username,  # Username dell'utente impersonato
            "impersonated_role": user.ruolo  # Ruolo dell'utente impersonato
        },
        expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_id": current_user.id_utente,  # ID dell'amministratore
        "username": current_user.username,  # Username dell'amministratore
        "role": "owner",  # Ruolo dell'amministratore
        "is_impersonated": True,
        "impersonated_id": user.id_utente,  # ID dell'utente impersonato
        "impersonated_username": user.username,  # Username dell'utente impersonato
        "impersonated_role": user.ruolo  # Ruolo dell'utente impersonato
    }
