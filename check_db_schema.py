#!/usr/bin/env python3
"""
Simple script to check database schema for cantieri table
"""

import psycopg2

def check_schema():
    try:
        # Connect to database
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            database='cantieri',
            user='postgres',
            password='Taranto'
        )
        cursor = conn.cursor()
        
        # Get column names for cantieri table
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'cantieri' 
            ORDER BY column_name
        """)
        
        columns = [row[0] for row in cursor.fetchall()]
        print("Columns in cantieri table:")
        for col in columns:
            print(f"  - {col}")
        
        # Check if 'nome' exists
        if 'nome' in columns:
            print("\n❌ Column 'nome' still exists - migration needed")
        else:
            print("\n✅ Column 'nome' does not exist")
            
        # Check if 'commessa' exists
        if 'commessa' in columns:
            print("✅ Column 'commessa' exists")
        else:
            print("❌ Column 'commessa' does not exist - migration needed")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    check_schema()
