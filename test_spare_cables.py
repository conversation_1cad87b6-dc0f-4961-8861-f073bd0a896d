#!/usr/bin/env python3
"""
Script per testare l'endpoint dei cavi spare e verificare se l'errore persiste
"""

import sys
import os

# Aggiungi il percorso del backend al path
sys.path.append(os.path.join(os.path.dirname(__file__), 'webapp'))

def test_spare_cables():
    try:
        from backend.database import get_db
        from backend.models.cantiere import Cantiere
        from sqlalchemy.orm import Session
        
        # Ottieni una sessione del database
        db_session = next(get_db())
        
        print("✅ Connessione al database riuscita")
        
        # Test 1: Verifica che esistano cantieri
        cantieri = db_session.query(Cantiere).all()
        print(f"📊 Trovati {len(cantieri)} cantieri nel database")
        
        if not cantieri:
            print("❌ Nessun cantiere trovato nel database")
            return False
        
        # Test 2: Prova ad accedere al campo commessa del primo cantiere
        primo_cantiere = cantieri[0]
        print(f"🔍 Test accesso campo 'commessa' del primo cantiere...")
        
        try:
            commessa = primo_cantiere.commessa
            print(f"✅ Campo 'commessa' accessibile: {commessa}")
        except AttributeError as e:
            print(f"❌ Errore accesso campo 'commessa': {e}")
            
            # Prova ad accedere al campo 'nome' (vecchio)
            try:
                nome = primo_cantiere.nome
                print(f"⚠️ Campo 'nome' ancora presente: {nome}")
                print("🔄 Migrazione del database necessaria!")
                return False
            except AttributeError:
                print("❌ Né 'commessa' né 'nome' sono accessibili")
                return False
        
        # Test 3: Simula la chiamata all'endpoint spare cables
        print(f"🔍 Test simulazione endpoint cavi spare per cantiere {primo_cantiere.id_cantiere}...")
        
        try:
            from webapp.backend.api.cavi_spare import get_cavi_spare
            from backend.core.security import get_current_active_user
            
            # Questo test verificherà se l'errore "Cantiere object has no attribute 'nome'" si verifica
            print("✅ Import dell'endpoint riuscito")
            print("ℹ️ L'errore dovrebbe essere risolto se tutti i riferimenti sono stati aggiornati")
            
        except Exception as e:
            print(f"❌ Errore durante l'import dell'endpoint: {e}")
            return False
        
        print("✅ Tutti i test sono passati!")
        return True
        
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'db_session' in locals():
            db_session.close()

if __name__ == "__main__":
    success = test_spare_cables()
    print(f"\n{'✅ Test completato con successo!' if success else '❌ Test fallito!'}")
    sys.exit(0 if success else 1)
